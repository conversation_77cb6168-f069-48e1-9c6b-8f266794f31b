use std::{
    collections::{HashMap, HashSet},
    fs,
    path::PathBuf,
    sync::{atomic::{AtomicUsize, Ordering}, Arc, Mutex, OnceLock},
};

use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Serialize;

#[derive(Debug, Serialize)]
pub struct DiskStat {
    name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    size: Option<u64>,
    #[serde(skip_serializing_if = "Vec::is_empty")]
    children: Vec<DiskStat>,
}

pub enum ScanFileInfo {
    File(u64),
    Directory,
}

pub struct DiskEntry {
    path: PathBuf,
    is_directory: bool,
    size: AtomicUsize,
    parent: Option<Arc<DiskEntry>>,
    children: Arc<Mutex<Vec<Arc<DiskEntry>>>>,
}

impl DiskEntry {
    pub fn new(path: PathBuf, size: usize, parent: Option<Arc<DiskEntry>>) -> Self {
        Self {
            path: path,
            is_directory: false,
            size: AtomicUsize::new(size),
            parent: parent,
            children: Arc::new(Mutex::new(Vec::new())),
        }
    }

    pub fn add_child(&self, child: Arc<DiskEntry>) {
        if !child.is_directory {
            let size = child.size.load(Ordering::Relaxed);
            self.update_size(size);
        }
        self.children.lock().unwrap().push(child);
    }

    fn update_size(&self, size: usize) {
        self.size.fetch_add(size, Ordering::Relaxed);
        if let Some(parent) = &self.parent {
            parent.update_size(size);
        }
    }
}

impl Default for DiskEntry {
    fn default() -> Self {
        Self {
            path: PathBuf::new(),
            is_directory: false,
            size: AtomicUsize::new(0),
            parent: None,
            children: Arc::new(Mutex::new(Vec::new())),
        }
    }
}


#[derive(Default)]
pub struct ParallelScanner {
    pub size_map: HashMap<PathBuf, u64>,
    pub path_map: HashMap<PathBuf, HashSet<PathBuf>>,

    pub files_processed: Arc<AtomicUsize>,
    pub dir_processed: Arc<AtomicUsize>,
    pub disk_entry: Arc<Mutex<DiskEntry>>,
}

impl ParallelScanner {
    // pub fn instance() -> &'static Arc<Mutex<Self>> {
    //     static INSTANCE: OnceLock<Arc<Mutex<ParallelScanner>>> = OnceLock::new();

    //     INSTANCE.get_or_init(|| {
    //         Arc::new(Mutex::new(Self {
    //             size_map: HashMap::new(),
    //             path_map: HashMap::new(),
    //             dir_processed: Arc::new(AtomicUsize::new(0)),
    //             files_processed: Arc::new(AtomicUsize::new(0)),
    //         }))
    //     })
    // }


    // pub fn get_stat(&self, path: &PathBuf) -> Option<DiskStat> {
    //     let name = path.file_name().unwrap().to_string_lossy().to_string();

    //     let mut stat = DiskStat {
    //         name: name,
    //         size: self.size_map.get(path).map(|s| *s),
    //         children: Vec::new(),
    //     };

    //     if let Some(child_paths) = self.path_map.get(path) {
    //         let (valid_paths, small_size_paths): (Vec<_>, Vec<_>) = child_paths.iter().partition(|p| {
    //             let result = self.size_map.get(*p);
    //             result.is_none() || result.is_some_and(|s|  *s > 1000 * 1000)
    //         });

    //         stat.children = valid_paths.iter().filter_map(|p| self.get_stat(*p)).collect::<Vec<_>>();
    //         let other_size = small_size_paths.iter().filter_map(|p| self.size_map.get(*p)).sum();
    //         if other_size > 0 {
    //             stat.children.push(DiskStat {
    //                 name: "*other*".to_string(),
    //                 size: Some(other_size),
    //                 children: Vec::new(),
    //             });
    //         }
    //     }

    //     if stat.size.is_none() && stat.children.is_empty() {
    //         return None;
    //     }
    //     Some(stat)

    // }

    pub fn scan(&self, path: &PathBuf) {
        // self.scan_path(path, 0, Arc::clone(&self.disk_entry));
    }

    pub fn scan_path(&self, path: &PathBuf, depth: usize, parent: Option<Arc<DiskEntry>>) -> anyhow::Result<()> {

        let metadata = fs::metadata(path)?;

        if metadata.is_file() {
            let size = metadata.len();
            self.files_processed.fetch_add(1, Ordering::Relaxed);
            let disk_entry = Arc::new(DiskEntry::new(path.to_path_buf(), , parent));


            return Ok(());
        }

        if !metadata.is_dir() {
            return Ok(());
        }

        self.dir_processed.fetch_add(1, Ordering::Relaxed);

        let mut paths_to_scan = Vec::new();
        let entries = fs::read_dir(path)?;
        for entry in entries {
            if let Ok(entry) = entry { 
                if entry.file_type()?.is_dir() {
                    paths_to_scan.push(entry.path());
                }
            }
        }

        paths_to_scan.par_iter().for_each(|entry| {
            self.scan_path(entry, depth + 1, Some(Arc::clone(&disk_entry)));
        });


        Ok(())
    }

    pub fn scan_directory(
        scanner: &'static Arc<Mutex<Self>>,
        path: &PathBuf,
    ) -> anyhow::Result<()> {
        let mut sub_dirs = Vec::new();
        // let mut path_set = HashSet::new();
        // let mut size_map = HashMap::new();

        

        for entry in fs::read_dir(&path)? {
            match entry {
                Ok(entry) => {
                    let path = entry.path();
                    let file_type = entry.file_type()?;

                    if file_type.is_dir() {
                        sub_dirs.push(entry.path());
                    }
                },
                Err(_) => {

                }
            }
            // match entry {
            //     Ok(entry) => match entry.file_type() {
            //         Ok(file_type) => {
            //             if file_type.is_symlink() {
            //                 continue;
            //             }

            //             let cur_path = entry.path();

            //             if file_type.is_file() || file_type.is_dir() {
            //                 // path_set.insert(cur_path.clone());
            //             }

            //             if file_type.is_file() {
            //                 if let Ok(metadata) = entry.metadata() {
            //                     let size = metadata.len();
            //                     // size_map.insert(cur_path, size);
            //                 }
            //             } else if file_type.is_dir() {
            //                 sub_dirs.push(cur_path);
            //             }
            //         }
            //         Err(err) => {
            //             println!("file type error: {:?}", err);
            //         }
            //     },
            //     Err(err) => {
            //         println!("entry error: {:?}", err);
            //     }
            // }
        }

        // {
        //     let mut scanner_guard = scanner.lock().unwrap();
        //     scanner_guard.path_map.insert(path.clone(), path_set);
        //     scanner_guard.size_map.extend(size_map);
        // }


        sub_dirs.par_iter().for_each(|sub_dir| {
            if let Err(err) = Self::scan_directory(scanner, sub_dir) {
                println!("scan directory error: {:?}", err);
            }
        });
        Ok(())
    }
}
